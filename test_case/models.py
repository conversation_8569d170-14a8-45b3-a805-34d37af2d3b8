from django.db import models
from scaffold.models.abstract.meta import ActiveModel

from core.models import DateModel


class TapdIterationsInfo(models.Model):
    id = models.AutoField(primary_key=True)
    tapd_iteration_id = models.CharField(max_length=50, unique=True, verbose_name="Tapd迭代ID")
    iteration_name = models.CharField(max_length=200, verbose_name="迭代名称")
    start_date = models.DateField(null=True, blank=True, verbose_name="开始日期")
    end_date = models.DateField(null=True, blank=True, verbose_name="结束日期")
    status = models.CharField(max_length=20, default="open", verbose_name="状态")
    workspace_id = models.CharField(max_length=50, verbose_name="工作空间ID")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta(object):
        db_table = "tapd_iterations_info"
        verbose_name = "Tapd迭代"
        ordering = ['-start_date']


class TapdRequirementInfo(models.Model):
    id = models.AutoField(primary_key=True, verbose_name='自增ID')
    iteration_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='迭代ID')
    tapd_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='TAPD需求ID')
    tapd_short_id = models.CharField(max_length=50, blank=True, verbose_name='TAPD需求短ID')
    parent_tapd_id = models.CharField(max_length=50, blank=True, verbose_name='父需求TAPDID')
    requirement_name = models.CharField(max_length=256, null=True, blank=True, verbose_name='需求标题')
    category = models.CharField(max_length=256, null=True, blank=True, verbose_name='业务线')
    developer = models.CharField(max_length=256, null=True, blank=True, verbose_name='开发人员')
    tester = models.CharField(max_length=256, null=True, blank=True, verbose_name='测试人员')
    producter = models.CharField(max_length=256, null=True, blank=True, verbose_name='产品(需求受理人)')
    submit_test_time = models.DateField(null=True, blank=True, verbose_name='提测时间')
    completed_test_time = models.DateField(null=True, blank=True, verbose_name='测试完成时间')
    status = models.CharField(max_length=20, null=True, blank=True, verbose_name='状态')
    priority = models.CharField(max_length=100, null=True, blank=True, verbose_name='优先级')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'tapd_requirement_info'
        verbose_name = 'TAPD需求信息表'


class TapdBugInfo(models.Model):
    SEVERITY_CHOICES = [
        ('fatal', '致命'),
        ('urgent', '严重'),
        ('normal', '一般'),
        ('prompt', '提示'),
        ('advice', '建议'),
    ]

    PRIORITY_CHOICES = [
        ('urgent', '紧急'),
        ('high', '高'),
        ('medium', '中'),
        ('low', '低'),
        ('insighificant', '无关紧要'),
        ('', '空'),
    ]

    ENVIRONMENT_CHOICES = [
        ('测试环境', '测试环境'),
        ('预发环境', '预发环境'),
        ('演示环境', '演示环境'),
        ('生产环境', '生产环境'),
    ]

    STATUS_CHOICES = [
        ('new', '新建'),
        ('in_progress', '开发处理中'),
        ('resolved', '已解决待发布'),
        ('unconfirmed', '待验证'),
        ('rejected', '已拒绝'),
        ('suspended', '挂起'),
        ('feedback', '转产品'),
        ('closed', '已关闭'),
        ('reopened', '重新打开'),
        ('PM_audited', '待生产验收'),
    ]

    id = models.AutoField(primary_key=True, verbose_name='自增ID')
    tapd_bug_id = models.CharField(max_length=50, verbose_name='缺陷ID')
    bug_short_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='缺陷短ID')
    title = models.CharField(max_length=255, verbose_name='缺陷标题')
    iteration_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='迭代ID')
    iteration_name = models.CharField(max_length=255, null=True, blank=True, verbose_name='迭代名称')
    severity = models.CharField(max_length=50, choices=SEVERITY_CHOICES, default='normal', verbose_name='严重程度')
    priority = models.CharField(max_length=50, choices=PRIORITY_CHOICES, null=True, blank=True, verbose_name='优先级')
    environment = models.CharField(max_length=50, choices=ENVIRONMENT_CHOICES, null=True, blank=True,
                                   verbose_name='发现环境')
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, null=True, blank=True, verbose_name='缺陷状态')
    developer = models.CharField(max_length=255, null=True, blank=True, verbose_name='开发人员(分号分隔)')
    current_owner = models.CharField(max_length=50, null=True, blank=True, verbose_name='处理人')
    creater = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    bug_create_time = models.DateTimeField(verbose_name='创建时间')
    project_id = models.CharField(max_length=50, verbose_name='项目ID')
    requirement_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='需求ID')
    create_time = models.DateTimeField(verbose_name='落库时间')
    update_time = models.DateTimeField(verbose_name='修改时间')

    class Meta:
        db_table = 'tapd_bugs_info'
        verbose_name = 'tapd缺陷信息'
        verbose_name_plural = 'tapd缺陷信息'
        indexes = [
            models.Index(fields=['title'], name='idx_title'),
            models.Index(fields=['tapd_bug_id'], name='idx_tapd_bug_id'),
            models.Index(fields=['bug_short_id'], name='idx_bug_short_id'),
            models.Index(fields=['requirement_id'], name='bug_requirement_id'),
            models.Index(fields=['status'], name='bug_status'),
            models.Index(fields=['bug_create_time'], name='bug_create_time'),
            models.Index(fields=['priority'], name='idx_priority'),
            models.Index(fields=['severity'], name='idx_severity'),
        ]

    def __str__(self):
        return f"{self.bug_short_id} - {self.title}"


class TapdHeadersConfig(models.Model):
    """
    TAPD接口请求头配置表
    存储不同类型的请求头配置信息
    """
    type = models.CharField(max_length=100, unique=True, verbose_name='配置类型',
                            help_text='用于区分不同的headers配置类型')
    headers = models.CharField(max_length=5000, verbose_name='头部信息配置', help_text='存储JSON格式的headers配置',
                               blank=True, null=True)
    body = models.CharField(max_length=5000, verbose_name='接口入参', help_text='存储JSON格式的headers配置', blank=True,
                            null=True)
    url = models.CharField(max_length=1000, verbose_name='接口url', help_text='存储JSON格式的headers配置', blank=True,
                           null=True)

    def get_headers_dict(self, **context):
        """将headers JSON字符串转换为字典(使用模板引擎处理变量替换)"""
        from string import Template
        import json
        import re

        if not self.headers:
            return {}

        try:
            # 1. 去除首尾空白字符
            headers_str = self.headers.strip()

            # 2. 变量替换（在解析JSON之前处理）
            if context:
                for key, value in context.items():
                    headers_str = headers_str.replace(f"${{{key}}}", str(value))

            # 3. 清理多余的转义字符（如果输入已包含转义的引号）
            headers_str = re.sub(r'\\([\\"])', r'\1', headers_str)  # 移除多余的反斜杠

            # 4. 替换单引号为双引号（仅在必要时）
            headers_str = headers_str.replace("'", '"')

            # 5. 解析JSON
            return json.loads(headers_str)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"错误位置: 行{e.lineno}列{e.colno}")
            print(f"问题内容: {headers_str[max(0, e.pos - 30):e.pos + 30]}")
            return {}
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return {}

    def get_body_dict(self, **context):
        """将body JSON字符串转换为字典(使用模板引擎处理变量替换)"""
        from string import Template
        import json
        if self.body:
            try:
                template = Template(self.body)
                body_str = template.safe_substitute(context)
                return json.loads(body_str)
            except (json.JSONDecodeError, ValueError) as e:
                print(f"解析body失败: {e}")
                return {}
        return {}

    class Meta:
        db_table = 'tapd_headers_config'
        verbose_name = 'TAPD头部配置'


class TestCase(ActiveModel):
    id = models.AutoField(primary_key=True)
    case_report_id = models.IntegerField(null=True, blank=True, verbose_name='用例导入报告记录id')
    business_line = models.CharField(max_length=255, null=True, blank=True, verbose_name='所属业务线名称')
    iteration_name = models.CharField(max_length=255, null=True, blank=True, verbose_name='归属迭代版本')
    case_scene = models.CharField(max_length=255, null=True, blank=True, verbose_name='用例标题')
    system = models.CharField(max_length=255, null=True, blank=True, verbose_name='所属系统')
    module = models.CharField(max_length=255, null=True, blank=True, verbose_name='功能模块')
    case_scene_id = models.CharField(max_length=255, null=True, blank=True, verbose_name='关联监控场景')
    premise = models.TextField(null=True, blank=True, verbose_name='前置条件')
    test_steps = models.TextField(null=True, blank=True, verbose_name='测试步骤')
    developer = models.CharField(max_length=255, null=True, blank=True, verbose_name='开发人员')
    requirement_id = models.CharField(max_length=100, null=True, blank=True, verbose_name='需求ID')
    requirement = models.CharField(max_length=1000, null=True, blank=True, verbose_name='需求标题')
    expected_result = models.TextField(null=True, blank=True, verbose_name='预期结果')
    case_type = models.CharField(max_length=10, null=True, blank=True, verbose_name='用例类型')
    keyword = models.CharField(max_length=255, null=True, blank=True, verbose_name='关键字')
    operator = models.CharField(max_length=255, null=True, blank=True, verbose_name='用例执行人')
    operator_result = models.CharField(max_length=255, null=True, blank=True, verbose_name='用例执行结果')
    operator_result_remark = models.CharField(max_length=255, null=True, blank=True, verbose_name='执行结果备注')
    file_url = models.CharField(max_length=1000, null=True, blank=True, verbose_name='执行结果图片')
    create_time = models.DateTimeField(auto_now_add=True, null=True, blank=True, verbose_name='创建时间')
    update_time = models.DateTimeField(null=True, blank=True, verbose_name='修改时间')
    update_person = models.CharField(max_length=128, null=True, blank=True, verbose_name='修改人')

    class Meta:
        db_table = 'test_case'


class ImportCaseReport(ActiveModel):
    # 自动递增的主键id
    id = models.AutoField(primary_key=True)
    # 业务线字段，使用枚举选项
    business_line = models.CharField(max_length=2)
    # 迭代ID
    iteration_id = models.CharField(max_length=100)
    # 迭代名称字段
    iteration_name = models.CharField(max_length=255)
    # 需求链接字段
    requirement_link = models.URLField(max_length=2000)
    # 冒烟结果统计字段
    smoke_result_statistics = models.CharField(max_length=255)
    # 操作人字段
    operator = models.CharField(max_length=100)
    # 更新时间字段，自动记录更新时间
    update_time = models.DateTimeField(auto_now=True)
    update_person = models.CharField(max_length=128, verbose_name='修改人')

    class Meta(object):
        db_table = "import_case_report"
        verbose_name = '导入用例记录表'


class NormTestCase(ActiveModel):
    """
    标准测试用例池表
    """
    id = models.AutoField(primary_key=True, verbose_name='自增主键')
    business_line = models.CharField(max_length=2, verbose_name='所属业务线')
    module = models.CharField(max_length=50, verbose_name='所属模块')
    keywords = models.CharField(max_length=50, verbose_name='关键字')
    requirement = models.CharField(max_length=1000, verbose_name='需求标题')
    case_scene = models.CharField(max_length=500, verbose_name='场景名称')
    premise = models.CharField(max_length=500, verbose_name='前置条件')
    test_steps = models.TextField(verbose_name='操作步骤')
    expected_result = models.CharField(max_length=500, verbose_name='预期结果')
    iteration_name = models.CharField(max_length=500, verbose_name='迭代名称')
    case_scene_id = models.CharField(max_length=100, verbose_name='自动化场景ID')
    creator = models.CharField(max_length=50, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    modifier = models.CharField(max_length=50, verbose_name='修改人')
    modify_time = models.DateTimeField(auto_now=True, verbose_name='修改时间')
    is_active = models.BooleanField(default=True, verbose_name='是否有效')

    class Meta:
        db_table = 'norm_test_case'
        verbose_name = '标准测试用例池表'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['business_line'], name='idx_business_line'),
            models.Index(fields=['module'], name='idx_module'),
            models.Index(fields=['keywords'], name='idx_keywords'),
            models.Index(fields=['iteration_name'], name='idx_iteration'),
        ]

    def __str__(self):
        return f"{self.id}-{self.requirement[:20]}"


class CaseMapping(ActiveModel):
    """
    用例匹配记录表模型
    对应MySQL表：case_mapping
    """
    id = models.AutoField(primary_key=True, verbose_name='主键ID')
    normtestcase_id = models.IntegerField(verbose_name='标准用例池ID')
    case_id = models.CharField(max_length=50, verbose_name='迭代核心用例ID')
    is_active = models.BooleanField(default=True, verbose_name='是否有效')
    mapping_type = models.CharField(
        max_length=20,
        default='auto',
        verbose_name='关联类型(auto/manual)'
    )
    relation_type = models.CharField(
        max_length=20,
        verbose_name='关系类型(merge/obsolete/refactor)'
    )
    created_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )
    status = models.CharField(
        max_length=20,
        default='active',
        verbose_name='映射状态'
    )
    operation_log = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name='操作日志'
    )

    class Meta:
        db_table = 'case_mapping'
        verbose_name = '用例匹配记录表'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['case_id'], name='idx_case_id'),
            models.Index(fields=['status'], name='idx_status'),
            models.Index(fields=['created_time'], name='idx_created_time'),
        ]

    def __str__(self):
        return f"{self.case_id} -> {self.normtestcase_id} ({self.relation_type})"


class MergeCaseIntermediate(ActiveModel):
    """
    用例合并中间表
    """
    STATUS_CHOICES = (
        ('1', '已推送'),
        ('0', '待推送'),
    )

    id = models.AutoField(primary_key=True, verbose_name='主键ID')
    case_report_id = models.IntegerField(blank=True, null=True,verbose_name='用例导入报告记录id')
    business_line = models.CharField(max_length=20, blank=True, null=True, verbose_name='所属业务线')
    module = models.CharField(max_length=50, blank=True, null=True, verbose_name='所属模块')
    keywords = models.CharField(max_length=1000, blank=True, null=True, verbose_name='关键字')
    requirement = models.CharField(max_length=500, blank=True, null=True, verbose_name='需求标题')
    case_scene = models.CharField(max_length=500, blank=True, null=True, verbose_name='场景名称')
    premise = models.TextField(blank=True, null=True, verbose_name='前置条件')
    test_steps = models.TextField(blank=True, null=True, verbose_name='操作步骤')
    expected = models.TextField(blank=True, null=True, verbose_name='预期结果')
    iteration_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='迭代名称')
    creator = models.CharField(max_length=50, blank=True, null=True, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    normtestcase = models.ForeignKey(
        'NormTestCase',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        db_column='normtestcase_id',
        verbose_name='原始用例池ID'
    )
    is_active = models.BooleanField(default=True, verbose_name='是否有效')
    status = models.SmallIntegerField(choices=STATUS_CHOICES, default=0, verbose_name='状态，0-待推送，1-已推送')

    class Meta:
        db_table = 'merge_case_intermediate'
        verbose_name = '用例合并中间表'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['business_line']),
            models.Index(fields=['module']),
            models.Index(fields=['iteration_name'], name='idx_iteration_name'),
            models.Index(fields=['creator'], name='idx_creator'),
            models.Index(fields=['normtestcase'], name='idx_normtestcase_id'),
        ]

    def __str__(self):
        return f"{self.id}-{self.requirement[:20]}" if self.requirement else str(self.id)

class AIGenerationRecord(ActiveModel):
    """
    AI生成记录表
    """
    iteration_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='迭代ID')
    business_id = models.CharField(max_length=255, null=True, blank=True, verbose_name='所属业务线')
    requirement_id = models.IntegerField(null=True, blank=True, verbose_name='需求ID')
    requirement_content = models.TextField(null=True, blank=True, verbose_name='需求内容')
    requirement_analyze_content = models.TextField(null=True, blank=True, verbose_name='需求分析内容')
    requirement_pic = models.CharField(max_length=1000, null=True, blank=True, verbose_name='需求图片URL')
    ui_link = models.CharField(max_length=1000, null=True, blank=True, verbose_name='UI设计链接')
    tech_link = models.CharField(max_length=1000, null=True, blank=True, verbose_name='技术文档链接')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_person = models.CharField(max_length=50, null=True, blank=True, verbose_name='创建人')
    is_active = models.BooleanField(default=True, verbose_name='是否有效')

    class Meta:
        db_table = 'ai_generation_records'
        verbose_name = 'AI生成记录'
        verbose_name_plural = 'AI生成记录'
        indexes = [
            models.Index(fields=['iteration_id'], name='idx_iteration_id'),
            models.Index(fields=['requirement_id'], name='idx_record_requirement'),
            models.Index(fields=['create_person'], name='idx_create_person'),
        ]

class AIGeneratedTestCase(ActiveModel):
    """
    AI生成测试用例表
    """
    STATUS_CHOICES = (
        ('1', '已召回'),
        ('0', '未召回'),
    )

    recognize_record_id = models.IntegerField(null=True, blank=True, verbose_name='识别记录ID')
    iteration_name = models.CharField(max_length=255, null=True, blank=True, verbose_name='归属迭代版本')
    requirement_id = models.IntegerField(null=True, blank=True, verbose_name='需求ID')
    business_line = models.CharField(max_length=255, null=True, blank=True, verbose_name='所属业务线名称')
    case_scene = models.CharField(max_length=255, null=True, blank=True, verbose_name='用例场景')
    system = models.CharField(max_length=255, null=True, blank=True, verbose_name='所属系统')
    module = models.CharField(max_length=255, null=True, blank=True, verbose_name='所属模块')
    premise = models.TextField(null=True, blank=True, verbose_name='前置条件')
    test_steps = models.TextField(null=True, blank=True, verbose_name='测试步骤')
    expected_result = models.TextField(null=True, blank=True, verbose_name='预期结果')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='1', verbose_name='召回状态')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_person = models.CharField(max_length=50, null=True, blank=True, verbose_name='更新人')
    is_active = models.BooleanField(default=True, verbose_name='是否有效')

    class Meta:
        db_table = 'ai_generated_test_cases'
        verbose_name = 'AI生成测试用例'
        verbose_name_plural = 'AI生成测试用例'
        indexes = [
            models.Index(fields=['recognize_record_id'], name='idx_recognize_record_id'),
            models.Index(fields=['requirement_id'], name='idx_requirement_id'),
            models.Index(fields=['system', 'module'], name='idx_system_module'),
        ]

    def __str__(self):
        return f"{self.system}-{self.module}: {self.case_scene}"