import json
import os
import re
import time
import traceback
from datetime import datetime

import xmind
from urllib.parse import unquote

import pandas as pd
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import F
from django.http import JsonResponse
from sqlalchemy.orm.sync import update

import ai_agent
from ai_agent.tools import generate_testcases
from ai_agent.tools.testcase_tools import get_analyze_prd, vectorize_test_cases, query_test_cases
from base.models import UserInfo
from core.commonUtils.commonUtil import CommonUtil
from core.utils import UserUtils, ResponseUtils, DateUtils
from interface_test.models import BusinessInfo
from . import serializers as s
from .exceptions import AppErrors
from .models import *


from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl import Workbook
from django.http import HttpResponse
from urllib.parse import quote
from io import BytesIO

from rest_framework import viewsets, status
from xmindparser import xmind_to_dict
from django.utils import timezone
from .servers.xmind_utils import opera_dict_to_list, case_data_into_db, export_to_xmind
import zipfile
from ai_agent.tools.jenkins_tools import trigger_build
from rest_framework.decorators import action
from rest_framework.response import Response
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class TestCaseInfoViewSet(viewsets.ModelViewSet):
    queryset = ImportCaseReport.objects.all()
    serializer_class = s.ImportCaseReportSerializer
    filter_fields = '__all__'
    ordering = ['-pk']
    """
    XMind测试用例管理
    """

    @action(methods=['POST'], detail=False)
    def import_xmind(self, request):
        """
        XMind文件导入接口
        参数:
        - xmind_url: XMind文件URL
        - business_line: 业务线
        - iteration_name: 迭代名称
        """
        # 从请求头中取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        temp = json.loads(request.body)

        try:
            xmind_url = temp.get('xmind_url', '')  # 文件路径(必填)
            business_line = temp.get('business_id', '')  # 业务线id(必填)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 导入用例" % (userinfo.username)
        # 日志记录
        CommonUtil().recordSystemLog(userinfo.username, content)  # 以系统日志类型记录到数据库
        # 显式的开启一个事务
        with transaction.atomic():
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                # 定义输入文件
                _data = xmind_to_dict(xmind_url)[0]['topic']  #
                cases_list = []

                # 创建导入记录
                import_report = ImportCaseReport.objects.create(
                    business_line=business_line,
                    operator=userinfo.chinese_name,
                    update_time=timezone.now(),
                    is_active=True
                )
                # 解析 XMind 数据
                for j in opera_dict_to_list(_data):
                    cases_list.append(j[1:])
                # 更新迭代名称
                title = _data['title']
                import_report.iteration_name = title
                import_report.save()
                # 处理并保存用例
                case_data_into_db(cases_list, business_line, title, import_report)
            except Exception as e:
                logger.error(f'导入失败：{e}')
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_IMPORT_FAIL)
            transaction.savepoint_commit(save_id)
        return ResponseUtils.return_success("导入成功")

    # 导入用例记录列表
    @action(methods=['POST'], detail=False)
    def import_case_report_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            iteration_name = temp.get('iteration_name', '')
            business_line = temp.get('business_id', '')
            operator = temp.get('creater', '')
            pagenum = temp.get('pagenum', 1)
            pagesize = temp.get('pagesize', 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询参数信息，查询条件：迭代名称=%s，业务线=%s , 创建者=%s " \
                  % (username, iteration_name, business_line, operator)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = ImportCaseReport.objects.filter(is_active=True)
        if business_line:
            obj = obj.filter(business_line=business_line)
        if iteration_name:
            obj = obj.filter(iteration_name__icontains=iteration_name)
        if operator:
            obj = obj.filter(operator__icontains=operator)
        obj = obj.order_by('-id')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_line"])
            item["businessName"] = businessInfo.business_name
            total_smoke_case = TestCase.objects.filter(case_report_id=item["id"], case_type=1).count()
            pass_smoke_case = TestCase.objects.filter(case_report_id=item["id"], case_type=1,
                                                      operator_result="1").count()
            item["total_count"] = total_smoke_case
            item["pass_count"] = pass_smoke_case
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['DELETE'], detail=True)
    def import_case_report_delete(self, request, pk):
        case_report_id = pk
        username = UserUtils.get_login_user(request).username
        reprot = ImportCaseReport.objects.get(id=case_report_id).__dict__
        TestCase.objects.filter(case_report_id=case_report_id).update(is_active=False, update_person=username,
                                                                      update_time=DateUtils.get_current_time())
        ImportCaseReport.objects.filter(id=case_report_id).update(is_active=False, update_person=username,
                                                                  update_time=DateUtils.get_current_time())
        # 判断是否存在下级接口
        content = "用户 %s 删除了用例导入记录：%s" % (username, reprot)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("用例删除成功")

    # 冒烟用例列表
    @action(methods=['POST'], detail=False)
    def smoke_case_list(self, request):
        """
        获取测试用例列表
        参数:
        - report_id: 导入报告ID (可选)
        - requirement: 需求字段 (可选)
        """
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            report_id = temp.get('report_id', '')
            operator_result = temp.get('operator_result', [])
            operator = temp.get('operator', '')
            requirement = temp.get('requirement', '')  # 新增需求字段
            developer = temp.get('developer', '')  # 新增开发人员
            pagenum = temp.get("pagenum", 1)
            pagesize = temp.get("pagesize", 10)
            # 处理 operator_result 为列表格式
            if isinstance(operator_result, str):
                # 兼容前端字符串
                operator_result = operator_result.split(',') if operator_result else []
            elif not isinstance(operator_result, list):
                # 非列表类型强制转为空列表
                operator_result = []
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询冒烟用例列表" \
                  % (username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = TestCase.objects.filter(case_report_id=report_id, case_type='1')
        # 新增开发人员查询
        if developer:
            obj = obj.filter(developer__icontains=developer)
        if operator:
            obj = obj.filter(operator__icontains=operator)
        if operator_result:
            obj = obj.filter(operator_result__in=operator_result)
        if requirement:
            obj = obj.filter(requirement__icontains=requirement)
        obj = obj.order_by('id')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_line"])
            item["businessName"] = businessInfo.business_name
            if item.get("file_url"):
                item["file_url"] = json.loads(item["file_url"])
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 导出用例列表
    @action(methods=['POST'], detail=False)
    def export_case(self, request):
        """
        导出用例接口
        """
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            report_id = temp.get('report_id')
            case_type = temp.get('case_type', "3")
            case_type_mapping = {
                "1": "冒烟",
                "2": "核心",
                "3": "全部"
            }
            # 获取用例类型枚举值
            case_type_value = case_type_mapping.get(case_type)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 导出 %s 用例" % (username, case_type_value)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        report_info = ImportCaseReport.objects.get(id=report_id)
        businessInfo = BusinessInfo.objects.get(business_id=report_info.business_line)
        businessName = businessInfo.business_name
        if case_type == "3":
            obj = TestCase.objects.filter(case_report_id=report_id, is_active=1).values(
                'id', 'business_line', 'iteration_name', 'case_scene', 'system', 'module', 'premise', 'test_steps',
                'expected_result')
        else:
            obj = TestCase.objects.filter(case_report_id=report_id, case_type=case_type, is_active=1).values(
                'id', 'business_line', 'iteration_name', 'case_scene', 'system', 'module', 'premise', 'test_steps',
                'expected_result')
        case_list = []
        if obj is not None:
            # 列表推导式
            case_list = [
                {
                    'business_line': businessName,
                    'iteration_name': case['iteration_name'],
                    'case_scene': case['case_scene'],
                    'system': case['system'],
                    'module': case['module'],
                    'premise': case['premise'],
                    'test_steps': case['test_steps'],
                    'expected_result': case['expected_result']
                }
                for case in obj if case['id']
            ]
        # records = json.loads(case_list.decode('utf-8'))
        # 创建一个新的工作簿和工作表
        workbook = Workbook()
        sheet = workbook.active
        sheet.title = "导出用例"
        # 写入表头
        headers = ["业务线", "迭代名称", "用例场景", "所属系统", "模块", "前置条件", "测试步骤", "预期结果"]
        sheet.append(headers)
        # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
        bold_font = Font(bold=True, size=15)  # 字体调大
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                             bottom=Side(style='thin'))
        grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

        for cell in sheet[1]:
            cell.font = bold_font
            cell.alignment = center_alignment
            cell.border = thin_border
            cell.fill = grey_fill  # 底色灰色
        # 设置列宽
        sheet.column_dimensions['A'].width = 20
        sheet.column_dimensions['B'].width = 20
        sheet.column_dimensions['C'].width = 20
        sheet.column_dimensions['D'].width = 20
        sheet.column_dimensions['E'].width = 20
        sheet.column_dimensions['F'].width = 20
        sheet.column_dimensions['G'].width = 20
        sheet.column_dimensions['H'].width = 20
        # 写入数据
        for case in case_list:
            row = [
                case['business_line'], case['iteration_name'], case['case_scene'],
                case['system'], case['module'], case['premise'], case['test_steps'],
                case['expected_result']
            ]
            sheet.append(row)
        # 设置单元格样式
        for row in sheet.iter_rows(min_row=2, max_col=sheet.max_column, max_row=sheet.max_row):
            for cell in row:
                cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')
                cell.font = Font(size=10)
        # 将工作簿保存到字节流
        output = BytesIO()
        workbook.save(output)

        # 创建 HTTP 响应并返回文件
        response = HttpResponse(output.getvalue(),
                                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = '导出用例.xlsx'  # 可根据实际需求动态生成文件名
        response['Content-Disposition'] = f'attachment; filename="{quote(filename)}"'

        return response

    # 更新导入用例
    @action(methods=['POST'], detail=False)
    def update_smoke_case(self, request):
        # 统一使用4空格缩进（PEP8规范）
        user = UserUtils.get_login_user(request)
        username = user.username
        chinese_name = user.chinese_name

        try:
            temp = json.loads(request.body)
            case_ids = temp.get('id', [])
            # 类型转换逻辑保持同级缩进
            if isinstance(case_ids, int):
                case_ids = [case_ids]
            elif not isinstance(case_ids, list):
                case_ids = []

            if not case_ids:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "用例 ID 不能为空")

            # 参数提取保持同级缩进
            file_url = temp.get('file_url', [])
            if not isinstance(file_url, (list, tuple)):
                file_url = [file_url]
            file_url = json.dumps(file_url)  # 序列化为JSON字符串

            # 其他参数保持对齐
            premise = temp.get('premise')
            test_steps = temp.get('test_steps')
            expected_result = temp.get('expected_result')
            operator_result = temp.get('operator_result')
            operator_result_remark = temp.get('operator_result_remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        # 时间处理保持同级缩进
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        queryset = TestCase.objects.filter(id__in=case_ids)

        # 修复关键点：字典定义与if/else保持同级缩进
        update_data = {
            'update_time': update_time
        }  # 字典闭合括号与定义对齐

        # 修复关键点：if/else控制流与字典定义同级
        if operator_result:
            update_data.update({
                'operator_result': operator_result,
                'operator_result_remark': operator_result_remark,
                'file_url': file_url,  # 重复字段会覆盖初始值
                'operator': chinese_name
            })
        else:
            update_data.update({
                'premise': premise,
                'test_steps': test_steps,
                'expected_result': expected_result
            })

        # 批量更新逻辑保持同级缩进
        updated_count = TestCase.objects.filter(id__in=case_ids).update(**update_data)

        # 日志记录保持同级缩进
        content = f"用户 {username} 批量编辑冒烟用例成功，影响记录数: {updated_count}"
        CommonUtil().recordSystemLog(username, content)
        return ResponseUtils.return_success(f"成功更新 {updated_count} 条记录")
        # if operator_result:
        #     # 保存图片
        #     if file_url and isinstance(file_url, (list, tuple)):
        #         file_url = json.dumps(file_url)
        #     TestCase.objects.filter(
        #         id=id).update(
        #         operator_result=operator_result,
        #         operator_result_remark=operator_result_remark,
        #         file_url=file_url,
        #         operator=username,
        #         update_time=update_time)
        #     content = "用户 %s 执行冒烟用例成功,ID: %s" % (
        #         username, id)
        # else:
        #     TestCase.objects.filter(
        #     id=id).update(
        #     premise=premise,
        #     test_steps=test_steps,
        #     expected_result=expected_result,
        #     update_time=update_time)
        #     content = "用户 %s 编辑冒烟用例成功,ID: %s" % (
        #         username, id)
        # # 日志记录
        # CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        # return ResponseUtils.return_success("编辑成功")

    # AI用例库列表
    @action(methods=['POST'], detail=False)
    def AI_test_case_list(self, request):
        # 从请求头中取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        temp = json.loads(request.body)
        try:
            iteration_name = temp.get('iteration_name', '')
            business_line = temp.get('business_id', '')
            modifier = temp.get('modifier', '')
            module = temp.get('module', '')
            case_scene_id = temp.get('case_scene_id', '')
            keywords = temp.get('keywords', '')
            case_scene = temp.get('case_scene', '')
            start_date = temp.get('start_date', '')
            end_date = temp.get('end_date', '')
            pagenum = temp.get('pagenum', 1)
            pagesize = temp.get('pagesize', 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 日志记录
        content = f"用户 {userinfo.username} 查询参数信息，查询条件：迭代名称={iteration_name}，业务线={business_line}, 修改者={modifier}"
        CommonUtil().recordSystemLog(userinfo.username, content)  # 以系统日志类型记录到数据库
        obj = NormTestCase.objects.filter(is_active=True)
        if business_line:
            obj = obj.filter(business_line=business_line)
        if iteration_name:
            obj = obj.filter(iteration_name__icontains=iteration_name)
        if module:
            obj = obj.filter(module__icontains=module)
        if case_scene:
            obj = obj.filter(case_scene__icontains=case_scene)
        if keywords:
            obj = obj.filter(keywords__icontains=keywords)
        if case_scene_id:
            obj = obj.filter(case_scene_id=case_scene_id)
        if modifier:
            obj = obj.filter(modifier__icontains=modifier)
        if start_date and end_date:
            obj = obj.filter(
                modify_time__gte=start_date,
                modify_time__lte=end_date
            )
        elif start_date:  # 只有开始日期
            obj = obj.filter(modify_time__gte=start_date)
        elif end_date:  # 只有结束日期
            obj = obj.filter(modify_time__lte=end_date)
        obj = obj.order_by('-modify_time')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_line"])  # 转化业务名称
            item["businessName"] = businessInfo.business_name
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 新增用例池用例
    @action(methods=['POST'], detail=False)
    def testcase_add(self, request):
        """
        添加测试用例
        :param request:
          business_line     必填  所属业务线
          module            必填  模块
          case_scene        必填  场景名称
          premise           必填  前置条件
          steps             必填  步骤
          expected_result   必填  预期结果
          keywords          非必填 关键字
          case_scene_id     非必填 自动化场景ID
          iteration_name    非必填 迭代名称
        :return:
        """
        # 获取登录用户的ID
        username = UserUtils.get_login_user(request).chinese_name
        # 提取参数
        temp = json.loads(request.body)
        try:
            business_line = temp['business_id']
            module = temp['module']
            premise = temp['premise']
            case_scene = temp['case_scene']
            test_steps = temp['test_steps']
            expected_result = temp['expected_result']
            keywords = temp.get('keywords', '')
            case_scene_id = temp.get('case_scene_id', '')
            iteration_name = temp.get('iteration_name', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

        # 创建测试用例对象并保存
        test_case = NormTestCase(
            business_line=business_line,
            module=module,
            case_scene=case_scene,
            premise=premise,
            test_steps=test_steps,
            expected_result=expected_result,
            keywords=keywords,
            case_scene_id=case_scene_id,
            iteration_name=iteration_name,
            creator=username,
            create_time=create_time,
            modifier=username,
            modify_time=create_time
        )

        test_case.save()

        content = f"用户 {username} 新增测试用例成功，用例ID：{test_case.id}"
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库

        return ResponseUtils.return_success("测试用例新增成功", data={"case_id": test_case.id})

    # 删除用例
    @action(methods=['DELETE'], detail=False)
    def testcase_delete(self, request):
        case_id = request.query_params.get('id')
        case_ids = request.query_params.get('ids', '')
        username = UserUtils.get_login_user(request).chinese_name
        if not case_id and not case_ids:
            return ResponseUtils.return_fail("请提供要删除的用例ID")

        # 处理批量删除
        if case_ids:
            try:
                id_list = [int(id) for id in case_ids.split(',') if id.strip()]
                if not id_list:
                    return ResponseUtils.return_fail("无效的ID列表")

                # 获取要删除的用例记录
                cases = NormTestCase.objects.filter(id__in=id_list, is_active=True)
                case_reports = list(cases.values())

                # 执行软删除
                cases.update(
                    is_active=False,
                    modifier=username,
                    modify_time=DateUtils.get_current_time()
                )

                content = f"用户 {username} 批量删除了 {len(id_list)} 条用例记录"
                CommonUtil().recordSystemLog(username, content)

                return ResponseUtils.return_success(f"成功删除 {len(id_list)} 条用例")

            except Exception as e:
                return ResponseUtils.return_fail(f"批量删除失败: {str(e)}")

        # 处理单个删除
        if case_id:
            try:
                case = NormTestCase.objects.get(id=case_id, is_active=True)
                report = case.__dict__

                case.is_active = False
                case.modifier = username
                case.modify_time = DateUtils.get_current_time()
                case.save()
                content = f"用户 {username} 删除了用例记录：{report}"
                CommonUtil().recordSystemLog(username, content)

                return ResponseUtils.return_success("用例删除成功")

            except NormTestCase.DoesNotExist:
                return ResponseUtils.return_fail("用例不存在或已被删除")

            except Exception as e:
                return ResponseUtils.return_fail(f"删除失败: {str(e)}")

    # 导出用例池用例
    # 假设是Django示例
    @action(methods=['POST'], detail=False)
    def export_test_case(self, request):
        # 获取查询参数
        business_id = request.data.get('business_id')
        if business_id == '':
            business_id = 0
        params = {
            'business_id': business_id,
            'iteration_name': request.data.get('iteration_name', ''),
            'modifier': request.data.get('modifier', ''),
            'case_scene_id': request.data.get('case_scene_id', ''),
            'keywords': request.data.get('keywords', ''),
            'module': request.data.get('module', ''),
            'case_scene': request.data.get('case_scene', ''),
            'creator': request.data.get('creator', ''),
            'start_date': request.data.get('start_date', ''),
            'end_date': request.data.get('end_date', ''),
        }

        # 根据参数查询数据
        queryset = NormTestCase.objects.filter(is_active=True)
        # 转换业务线名称
        businessInfo = BusinessInfo.objects.get(business_id=params['business_id'])
        businessName = businessInfo.business_name

        if params['business_id']:
            queryset = queryset.filter(business_line=params['business_id'])
        if params['iteration_name']:
            queryset = queryset.filter(iteration_name__icontains=params['iteration_name'])
        if params['module']:
            queryset = queryset.filter(module__icontains=params['module'])
        if params['case_scene']:
            queryset = queryset.filter(case_scene__icontains=params['case_scene'])
        if params['keywords']:
            queryset = queryset.filter(keywords__icontains=params['keywords'])
        if params['case_scene_id']:
            queryset = queryset.filter(case_scene_id=params['case_scene_id'])
        if params['modifier']:
            queryset = queryset.filter(modifier__icontains=params['modifier'])
        if params['start_date'] and params['end_date']:
            queryset = queryset.filter(
                create_time__range=(params['start_date'], params['end_date'])
            )
        case_list = []
        if queryset is not None:
            # 列表推导式
            case_list = [
                {
                    'business_line': businessName,
                    'iteration_name': case.iteration_name,
                    'module': case.module,
                    'case_scene': case.case_scene,
                    'premise': case.premise,
                    'test_steps': case.test_steps,
                    'expected_result': case.expected_result,
                    'keywords': case.keywords,
                    'case_scene_id': case.case_scene_id,
                    'modifier': case.modifier,
                }
                for case in queryset if case.id
            ]
        # records = json.loads(case_list.decode('utf-8'))
        # 创建一个新的工作簿和工作表
        workbook = Workbook()
        sheet = workbook.active
        sheet.title = "导出用例"
        # 写入表头
        headers = ["业务线", "迭代名称", "模块", "用例场景", "前置条件", "测试步骤", "预期结果", "最后修改人"]
        sheet.append(headers)
        # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
        bold_font = Font(bold=True, size=15)  # 字体调大
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                             bottom=Side(style='thin'))
        grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

        for cell in sheet[1]:
            cell.font = bold_font
            cell.alignment = center_alignment
            cell.border = thin_border
            cell.fill = grey_fill  # 底色灰色
        # 设置列宽
        sheet.column_dimensions['A'].width = 20
        sheet.column_dimensions['B'].width = 20
        sheet.column_dimensions['C'].width = 20
        sheet.column_dimensions['D'].width = 20
        sheet.column_dimensions['E'].width = 20
        sheet.column_dimensions['F'].width = 20
        sheet.column_dimensions['G'].width = 20
        sheet.column_dimensions['H'].width = 20
        # 写入数据
        for case in case_list:
            row = [
                case['business_line'], case['iteration_name'], case['module'], case['case_scene'],
                case['premise'], case['test_steps'],
                case['expected_result'], case['modifier']
            ]
            sheet.append(row)
        # 设置单元格样式
        for row in sheet.iter_rows(min_row=2, max_col=sheet.max_column, max_row=sheet.max_row):
            for cell in row:
                cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')
                cell.font = Font(size=10)
        # 将工作簿保存到字节流
        output = BytesIO()
        workbook.save(output)

        # 创建 HTTP 响应并返回文件
        response = HttpResponse(output.getvalue(),
                                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = '导出用例.xlsx'  # 可根据实际需求动态生成文件名
        response['Content-Disposition'] = f'attachment; filename="{quote(filename)}"'

        return response

    @action(methods=['POST'], detail=False)
    def import_case_excel(self, request):
        username = UserUtils.get_login_user(request).chinese_name

        try:
            temp = json.loads(request.body)
            raw_url = temp['file_url']
            excel_url = raw_url.replace('%5C', '\\')  # 处理反斜杠编码
            excel_url = excel_url.encode('latin1').decode('utf-8')  # 处理中文
            excel_url = os.path.abspath(os.path.normpath(excel_url))  # 规范化路径

            # 验证路径
            if not os.path.exists(excel_url):
                raise FileNotFoundError(f"文件不存在: {excel_url}")

            # 读取Excel（使用openpyxl引擎）
            df = pd.read_excel(excel_url, engine='openpyxl')
            df.columns = df.columns.str.strip()

            # 修正后的列映射
            column_mapping = {
                '业务线': 'business_line',
                '迭代名称': 'iteration_name',
                '用例场景': 'case_scene',
                '模块': 'module',
                '前置条件': 'premise',
                '测试步骤': 'test_steps',
                '预期结果': 'expected_result'
            }

            # 数据转换
            df = df.rename(columns=column_mapping)
            df = df.dropna(subset=['business_line', 'module', 'test_steps'])

            # 创建实例（严格匹配模型字段）
            cases_to_create = []
            for _, row in df.iterrows():
                case = NormTestCase(
                    business_line=row.get('business_line', ''),
                    module=row.get('module', ''),
                    keywords=row.get('keywords', ''),
                    requirement=row.get('requirement', '默认需求'),
                    case_scene=row.get('case_scene', ''),
                    premise=row.get('premise',''),
                    test_steps=row['test_steps'],
                    expected_result=row['expected_result'],
                    iteration_name=row.get('iteration_name', '默认迭代'),
                    creator=username,
                    modifier=username
                )
                cases_to_create.append(case)

            NormTestCase.objects.bulk_create(cases_to_create)
            return ResponseUtils.return_success("导入成功")
            # return ResponseUtils.return_success({"success": True})

        except Exception as e:
            logger.error(f"路径处理失败: {str(e)}")
            return ResponseUtils.return_fail(AppErrors.ERROR_IMPORT_FAIL, str(e))

    # 编辑用例池用例
    @action(methods=['POST'], detail=False)
    def testcase_update(self, request):
        """
        更新测试用例
        :param request:
          case_id           必填  测试用例ID
          business_line     非必填  所属业务线
          module            非必填  模块
          case_scene        非必填  场景名称
          premise           非必填  前置条件
          steps             非必填  步骤
          expected_result   非必填  预期结果
          keywords          非必填  关键字
          case_scene_id     非必填  自动化场景ID
          iteration_name    非必填  迭代名称
        :return:
        """
        # 获取登录用户
        username = UserUtils.get_login_user(request).chinese_name

        try:
            # 提取参数
            temp = json.loads(request.body)
            case_id = temp['id']  # 必填参数

            # 获取要更新的测试用例
            test_case = NormTestCase.objects.get(id=case_id, is_active=1)

            # 更新字段 - 只更新提供的字段
            update_fields = []
            modify_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

            if 'business_id' in temp:
                test_case.business_line = temp['business_id']
                update_fields.append('business_line')

            if 'module' in temp:
                test_case.module = temp['module']
                update_fields.append('module')

            if 'case_scene' in temp:
                test_case.case_scene = temp['case_scene']
                update_fields.append('case_scene')

            if 'premise' in temp:
                test_case.premise = temp['premise']
                update_fields.append('premise')

            if 'test_steps' in temp:
                test_case.test_steps = temp['test_steps']
                update_fields.append('test_steps')

            if 'expected_result' in temp:
                test_case.expected_result = temp['expected_result']
                update_fields.append('expected_result')

            if 'keywords' in temp:
                test_case.keywords = temp['keywords']
                update_fields.append('keywords')

            if 'case_scene_id' in temp:
                test_case.case_scene_id = temp['case_scene_id']
                update_fields.append('case_scene_id')

            if 'iteration_name' in temp:
                test_case.iteration_name = temp['iteration_name']
                update_fields.append('iteration_name')

            # 更新修改人和修改时间
            test_case.modifier = username
            test_case.modify_time = modify_time
            update_fields.extend(['modifier', 'modify_time'])

            # 只保存有变化的字段
            if update_fields:
                test_case.save(update_fields=update_fields)

                content = f"用户 {username} 更新测试用例成功，用例ID：{test_case.id}"
                CommonUtil().recordSystemLog(username, content)

                return ResponseUtils.return_success("测试用例更新成功", data={"case_id": test_case.id})
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "未提供任何可更新字段")

        except NormTestCase.DoesNotExist:
            logger.error(f'测试用例不存在，ID：{case_id}')
            return ResponseUtils.return_fail(AppErrors.ERROR_DATA_NOT_EXIST, "测试用例不存在或已被删除")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'更新测试用例异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 核心用例列表
    @action(methods=['POST'], detail=False)
    def core_case_list(self, request):
        """
        获取测试用例列表
        参数:
        - report_id: 导入报告ID (可选)
        - requirement: 需求字段 (可选)
        """
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            case_scene = temp.get('case_scene', '')  # 用例场景
            module = temp.get('module', '')  # 新增模块字段
            system = temp.get('system', '')  # 新增系统字段
            report_id = temp.get('report_id', '')
            requirement = temp.get('requirement', '')  # 新增需求字段
            pagenum = temp.get("pagenum", 1)
            pagesize = temp.get("pagesize", 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询冒烟用例列表" \
                  % (username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = TestCase.objects.filter(case_report_id=report_id, case_type='2', is_active='1')  # 用例导入报告记录id+核心用例类型字段
        if module:
            obj = obj.filter(module__icontains=module)
        if case_scene:
            obj = obj.filter(case_scene__icontains=case_scene)
        if system:
            obj = obj.filter(system__icontains=system)
        if requirement:
            obj = obj.filter(requirement__icontains=requirement)

        obj = obj.order_by('id')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_line"])
            item["businessName"] = businessInfo.business_name
        return ResponseUtils.return_success("查询成功", response_json)

    # 删除核心用例
    @action(methods=['DELETE'], detail=True)
    def import_core_report_delete(self, request, pk):
        core_report_id = pk
        username = UserUtils.get_login_user(request).username
        TestCase.objects.filter(id=core_report_id).update(is_active=False, update_person=username,
                                                          update_time=DateUtils.get_current_time())
        # 判断是否存在下级接口
        content = "用户 %s 删除了核心用例：%s" % (username, core_report_id)

        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("用例删除成功")

    # 新增核心用例
    @action(methods=['POST'], detail=False)
    def corecase_add(self, request):
        """
        添加测试用例
        :param request:
          business_line     必填  所属业务线
          module            必填  模块
          requirements      非必填  需求标题
          system            非必填  所属系统
          case_scene        必填  场景名称
          premise           必填  前置条件
          steps             必填  步骤
          expected_result   必填  预期结果
          keyword          非必填 关键字
          case_scene_id     非必填 自动化场景ID
          iteration_name    非必填 迭代名称
        :return:
        """
        # 获取登录用户的ID
        username = UserUtils.get_login_user(request).chinese_name
        # 提取参数
        temp = json.loads(request.body)
        try:
            business_line = temp['business_id']
            module = temp['module']
            requirement = temp.get('requirements', '')
            system = temp.get('system', '')
            premise = temp['premise']
            case_scene = temp['case_scene']
            test_steps = temp['test_steps']
            expected_result = temp['expected_result']
            keyword = temp.get('keywords', '')
            case_scene_id = temp.get('case_scene_id', '')
            iteration_name = temp.get('iteration_name', '')
            case_report_id = temp.get('case_report_id', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

        # 创建测试用例对象并保存
        test_case = TestCase(
            business_line=business_line,
            case_report_id=case_report_id,
            module=module,
            requirement=requirement,
            system=system,
            case_scene=case_scene,
            premise=premise,
            test_steps=test_steps,
            expected_result=expected_result,
            keyword=keyword,
            case_scene_id=case_scene_id,
            iteration_name=iteration_name,
            create_time=create_time,
            update_person=username,
            update_time=create_time,
            is_active=1,  # 假设1表示活跃/有效
            case_type=2,  # 2表示核心用例
            operator_result=0  # 0表示未执行
        )

        test_case.save()

        content = f"用户 {username} 新增测试用例成功，用例ID：{test_case.id}"
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库

        return ResponseUtils.return_success("测试用例新增成功", data={"case_id": test_case.id})

    # 编辑核心用例
    @action(methods=['POST'], detail=False)
    def update_core_case(self, request):
        # 统一使用4空格缩进（PEP8规范）
        user = UserUtils.get_login_user(request)
        username = user.username
        chinese_name = user.chinese_name

        try:
            temp = json.loads(request.body)
            case_ids = temp.get('id', [])
            # 类型转换逻辑保持同级缩进
            if isinstance(case_ids, int):
                case_ids = [case_ids]
            elif not isinstance(case_ids, list):
                case_ids = []

            if not case_ids:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "用例 ID 不能为空")

            # 参数提取保持同级缩进
            file_url = temp.get('file_url', [])
            if not isinstance(file_url, (list, tuple)):
                file_url = [file_url]
            file_url = json.dumps(file_url)  # 序列化为JSON字符串

            # 其他参数保持对齐
            premise = temp.get('premise')
            test_steps = temp.get('test_steps')
            expected_result = temp.get('expected_result')
            operator_result = temp.get('operator_result')
            operator_result_remark = temp.get('operator_result_remark')
            case_scene = temp.get('case_scene')
            case_scene_id = temp.get('case_scene_id')
            keyword = temp.get('keyword')
            requirement = temp.get('requirement')
            module = temp.get('module')
            system = temp.get('system')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        # 时间处理保持同级缩进
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        queryset = TestCase.objects.filter(id__in=case_ids)

        # 修复关键点：字典定义与if/else保持同级缩进
        update_data = {
            'update_time': update_time
        }  # 字典闭合括号与定义对齐

        # 修复关键点：if/else控制流与字典定义同级
        if operator_result:
            update_data.update({
                'operator_result': operator_result,
                'operator_result_remark': operator_result_remark,
                'file_url': file_url,  # 重复字段会覆盖初始值
                'operator': chinese_name
            })
        else:
            update_data.update({
                'premise': premise,
                'test_steps': test_steps,
                'expected_result': expected_result,
                'case_scene': case_scene,
                'keyword': keyword,
                'case_scene_id': case_scene_id,
                'requirement': requirement,
                'module': module,
                'system': system
            })

        # 批量更新逻辑保持同级缩进
        updated_count = TestCase.objects.filter(id__in=case_ids).update(**update_data)

        # 日志记录保持同级缩进
        content = f"用户 {username} 批量编辑冒烟用例成功，影响记录数: {updated_count}"
        CommonUtil().recordSystemLog(username, content)
        return ResponseUtils.return_success(f"成功更新 {updated_count} 条记录")

    # AI根据需求分析内容生成测试用例
    @action(methods=['POST'], detail=False)
    def ai_generate_testcases(self, request):
        """
        AI根据需求分析生成测试用例
        :param
         - content  需求文本块(必填)
         - pic 需求图片
         - prd_link prd地址
         - tech_link 技术文档地址
        """
        user_info = UserUtils.get_login_user(request)
        username = user_info.username
        chinese_name = user_info.chinese_name
        temp = json.loads(request.body)
        try:
            requirement_content = temp['requirement_content']
            requirementAnalyzePrd = temp['requirementAnalyzePrd']
            requirement_id = temp.get('requirement_id', '')
            business_id = temp.get('business_id', '')
            requirement_pic = temp.get('requirement_pic', '')
            tech_link = temp.get('tech_link', '')
            ui_link = temp.get('ui_link', '')
            # 处理需求图片
            if not isinstance(requirement_pic, (list, tuple)):
                requirement_pic = [requirement_pic]
            requirement_pic = json.dumps(requirement_pic)  # 序列化为JSON字符串
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 显式的开启一个事务
        with (transaction.atomic()):
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                iteration_info = TapdRequirementInfo.objects.filter(tapd_short_id=requirement_id).first()
                # 创建导入记录
                ai_generation_records = AIGenerationRecord.objects.create(
                    requirement_id=requirement_id,
                    business_id=business_id,
                    iteration_id=iteration_info.iteration_id if iteration_info else None,
                    requirement_content=requirement_content,
                    requirement_analyze_content=requirementAnalyzePrd,
                    tech_link=tech_link,
                    ui_link=ui_link,
                    requirement_pic=requirement_pic,
                    create_person=chinese_name,
                    create_time=timezone.now(),
                    is_active=True
                )
                ai_generation_records.save()
                logger.info(f"AI开始生成需求【{requirement_id}】的用例")
                test_cases = generate_testcases(content=requirementAnalyzePrd)
                logger.info(f"AI生成测试用例成功:{test_cases}")
                # 将生成的测试用例保存到数据库
                if test_cases and 'test_cases' in test_cases:
                    for case in test_cases['test_cases']:
                        AIGeneratedTestCase.objects.create(
                            recognize_record_id=ai_generation_records.id,
                            requirement_id=requirement_id,
                            case_scene=case.get('场景', ''),
                            premise=case.get('前提', ''),
                            system=case.get('系统', ''),
                            module=case.get('模块', ''),
                            test_steps='\n'.join(case.get('步骤', [])) if isinstance(case.get('步骤', []),
                                                                                     list) else case.get('步骤', ''),
                            expected_result='\n'.join(case.get('预期结果', [])) if isinstance(case.get('预期结果', []),
                                                                                              list) else case.get(
                                '预期结果', ''),
                            status='0',  # 默认为未召回状态
                            update_person=chinese_name,
                            create_time=timezone.now(),
                            is_active=True
                        )
            except Exception as e:
                logger.error(f'用例生成失败：{e}')
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_GENERATION_TESTCASE_FAIL)
            transaction.savepoint_commit(save_id)
        content = "用户 %s 生成需求:%s的测试用例" % (username, requirement_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("用例生成成功", {'id': ai_generation_records.id})

    @action(methods=['POST'], detail=False)
    def ai_get_analyze_prd(self, request):
        """
        AI根据需求文档生成需求分析
        :param
         - content  需求文本块(必填)
         - pic 需求图片
         - prd_link prd地址
         - tech_link 技术文档地址
        """
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            requirement_content = temp['requirement_content']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        prd_analyze = get_analyze_prd(content=requirement_content)
        return ResponseUtils.return_success("需求分析成功", prd_analyze)

    # AI生成用例记录列表
    @action(methods=['POST'], detail=False)
    def recognize_record_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            requirement_id = temp.get('requirement_id', '')
            iteration_name = temp.get('iteration_name', '')
            create_person = temp.get('create_person', '')
            pagenum = temp.get('pagenum', 1)
            pagesize = temp.get('pagesize', 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询AI生成用例记录列表，查询条件：迭代名称=%s，需求ID=%s , 创建者=%s " \
                  % (username, iteration_name, requirement_id, create_person)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = AIGenerationRecord.objects.filter(is_active=True)
        if iteration_name:
            iteration_ids = TapdIterationsInfo.objects.filter(iteration_name__icontains=iteration_name).values_list(
                'tapd_iteration_id', flat=True)
            obj = obj.filter(iteration_id__in=iteration_ids)
        if requirement_id:
            obj = obj.filter(requirement_id=requirement_id)
        if create_person:
            obj = obj.filter(create_person__icontains=create_person)
        obj = obj.order_by('-id')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        for item in data_list:
            # 获取迭代名称
            iteration_id = item.get("iteration_id", "")
            if iteration_id:
                interation_info = TapdIterationsInfo.objects.filter(tapd_iteration_id=iteration_id).first()  # 根据拼音查询用户
                item["iteration_name"] = interation_info.iteration_name if interation_info else ""
            else:
                item["iteration_name"] = ""  # 处理空值
            # 获取总用例数
            total_ai_test_case = AIGeneratedTestCase.objects.filter(recognize_record_id=item["id"], is_active=1).count()
            item["total_ai_test_case"] = total_ai_test_case
            # 获取已召回用例数
            recall_ai_test_case = AIGeneratedTestCase.objects.filter(recognize_record_id=item["id"], is_active=1,
                                                                     status=1).count()
            item["recall_ai_test_case"] = recall_ai_test_case
            # 需求图片转换成json格式
            if item["requirement_pic"]:
                if isinstance(item["requirement_pic"], list):
                    item["requirement_pic"] = item["requirement_pic"]
                if isinstance(item["requirement_pic"], str):
                    try:
                        item["requirement_pic"] = json.loads(item["requirement_pic"])  # 尝试解析 JSON 字符串
                    except json.JSONDecodeError:
                        item["requirement_pic"] = [item["requirement_pic"]]
            # 获取业务线名称
            businessInfo = BusinessInfo.objects.get(business_id=item["business_id"])
            item["business_name"] = businessInfo.business_name
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 获取AI用例用例详情
    @action(methods=['GET'], detail=False)
    def get_case_detail(self, request):
        username = UserUtils.get_login_user(request).username
        try:
            test_case_id = request.GET.get('test_case_id', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查看AI用例 %s 详情" % (username, test_case_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        try:
            test_case = AIGeneratedTestCase.objects.get(id=test_case_id)
            # 将测试步骤从字符串转换为JSON格式
            try:
                test_steps = json.loads(test_case.test_steps)
            except (json.JSONDecodeError, TypeError):
                # 如果不是有效的JSON字符串，则保持原样
                test_steps = test_case.test_steps

            # 构建返回数据结构
            case_data = {
                "id": test_case.id,
                "requirement_id": test_case.requirement_id,
                "case_scene": test_case.case_scene,
                "system": test_case.system,
                "module": test_case.module,
                "premise": test_case.premise,
                "test_steps": test_steps,  # 已转换为JSON对象
                "expected_result": test_case.expected_result,
                "status": test_case.status,
                "update_person": test_case.update_person,
                "create_time": test_case.create_time.strftime("%Y-%m-%d %H:%M:%S") if test_case.create_time else "",
                "is_active": test_case.is_active
            }
            return ResponseUtils.return_success("用例详情获取成功", case_data)
        except Exception as e:
            logger.error(f'用例详情获取失败：{e}')
            return ResponseUtils.return_fail(AppErrors.ERROR_GET_CASE_DETAIL_FAIL)

    # 从需求维度导出用例
    @action(methods=['GET'], detail=False)
    def export_to_xmind_by_requirement(self, request):
        username = UserUtils.get_login_user(request).username
        try:
            requirement_id = request.GET.get('requirement_id', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 导出需求 %s AI生成用例" % (username, requirement_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        # 获取需求信息
        requirement_info = TapdRequirementInfo.objects.filter(tapd_short_id=requirement_id).first()
        # 获取迭代信息
        iteration_info = TapdIterationsInfo.objects.filter(tapd_iteration_id=requirement_info.iteration_id).first()

        # 获取用例
        test_case = AIGeneratedTestCase.objects.filter(requirement_id=requirement_id, is_active=True, status=0)
        response = export_to_xmind(test_case, requirement_info, iteration_info)
        return response

    # 从AI生成用例记录维度导出用例
    @action(methods=['GET'], detail=False)
    def export_to_xmind_by_record(self, request):
        username = UserUtils.get_login_user(request).username
        try:
            recognize_record_id = request.GET.get('recognize_record_id', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 导出识别id为 %s 的AI生成用例" % (username, recognize_record_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        # 获取识别记录
        recognize_record = AIGenerationRecord.objects.filter(id=recognize_record_id).first()
        # 获取需求信息
        requirement_info = TapdRequirementInfo.objects.filter(tapd_short_id=recognize_record.requirement_id).first()
        # 获取迭代信息
        iteration_info = TapdIterationsInfo.objects.filter(tapd_iteration_id=recognize_record.iteration_id).first()
        # 获取用例
        test_case = AIGeneratedTestCase.objects.filter(recognize_record_id=recognize_record_id, is_active=True,
                                                       status=1)
        response = export_to_xmind(test_case, requirement_info, iteration_info)
        return response

    # AI生成测试用例记录列表
    @action(methods=['POST'], detail=False)
    def ai_recognize_record_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            requirement_id = temp.get('requirement_id', '')
            iteration_name = temp.get('iteration_name', '')
            business_id = temp.get('business_id', '')
            create_person = temp.get('create_person', '')
            pagenum = temp.get('pagenum', 1)
            pagesize = temp.get('pagesize', 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询AI生成用例记录，查询条件：需求ID=%s，迭代名称=%s，业务线=%s，创建人=%s " \
                  % (username, requirement_id, iteration_name, business_id, create_person)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = AIGenerationRecord.objects.filter(is_active=True)
        if requirement_id:
            obj = obj.filter(requirement_id=requirement_id)
        if iteration_name:
            iteration_ids = TapdIterationsInfo.objects.filter(iteration_name__icontains=iteration_name).values_list(
                'tapd_iteration_id', flat=True)
            obj = obj.filter(iteration_id__in=iteration_ids)
        if create_person:
            obj = obj.filter(create_person__icontains=create_person)
        if business_id:
            obj = obj.filter(business_id=business_id)
        obj = obj.order_by('-id')  # 排序
        paginator = Paginator(obj, pagesize)  # 条数
        data = paginator.page(pagenum)  # 页码
        data_list = list(data.object_list.values())  # 查到第一页的数据
        for item in data_list:
            # 获取迭代名称
            iteration_id = item.get("iteration_id", "")
            if iteration_id:
                interation_info = TapdIterationsInfo.objects.filter(tapd_iteration_id=iteration_id).first()  # 根据拼音查询用户
                item["iteration_name"] = interation_info.iteration_name if interation_info else ""
            else:
                item["iteration_name"] = ""  # 处理空值
            # 获取总用例数
            total_ai_test_case = AIGeneratedTestCase.objects.filter(recognize_record_id=item["id"], is_active=1).count()
            item["total_ai_test_case"] = total_ai_test_case
            # 获取已召回用例数
            recall_ai_test_case = AIGeneratedTestCase.objects.filter(recognize_record_id=item["id"], is_active=1,
                                                                     status=1).count()
            item["recall_ai_test_case"] = recall_ai_test_case
            # 需求图片转换成json格式
            if item["requirement_pic"]:
                if isinstance(item["requirement_pic"], list):
                    item["requirement_pic"] = item["requirement_pic"]
                if isinstance(item["requirement_pic"], str):
                    try:
                        item["requirement_pic"] = json.loads(item["requirement_pic"])  # 尝试解析 JSON 字符串
                    except json.JSONDecodeError:
                        item["requirement_pic"] = [item["requirement_pic"]]
            # 获取业务线名称
            businessInfo = BusinessInfo.objects.get(business_id=item["business_id"])
            item["business_name"] = businessInfo.business_name
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # AI生成用例列表
    @action(methods=['POST'], detail=False)
    def ai_generate_test_case_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            system = temp.get('system', '')
            # 记录id不能为空，只查询单次记录的用例列表，带id查询，所以该字段不能带''
            recognize_record_id = temp.get('recognize_record_id')
            module = temp.get('module', '')
            status = temp.get('status', '')
            pagenum = temp.get('pagenum', 1)
            pagesize = temp.get('pagesize', 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询AI生成用例列表，查询条件：系统=%s，模块=%s , 状态=%s " \
                  % (username, system, module, status)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        # 后面的recognize_record_id代表录入的记录id（入参的字段）传给前面的recognize_record_id（数据库内的字段名）
        obj = AIGeneratedTestCase.objects.filter(is_active=True, recognize_record_id=recognize_record_id)
        # 带查询条件对前面obj列表进行查询
        if system:
            obj = obj.filter(system=system)
        if module:
            obj = obj.filter(module=module)
        if status:
            obj = obj.filter(status=status)
        # 按照字段进行排序
        obj = obj.order_by('-id')
        # 分页，直接复制就行
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        # 查询到第一页的数据
        data_list = list(data.object_list.values())
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 更新AI生成用例
    @action(methods=['POST'], detail=False)
    def update_ai_test_case(self, request):
        user = UserUtils.get_login_user(request)
        username = user.username
        chinese_name = user.chinese_name
        try:
            temp = json.loads(request.body)
            case_id = temp.get('id')
            case_scene = temp.get('case_scene', '')
            system = temp.get('system', '')
            module = temp.get('module', '')
            premise = temp.get('premise', '')
            test_steps = temp.get('test_steps', '')
            expected_result = temp.get('expected_result', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        AIGeneratedTestCase.objects.filter(id=case_id).update(case_scene=case_scene,
                                                              system=system,
                                                              premise=premise,
                                                              module=module,
                                                              test_steps=test_steps,
                                                              expected_result=expected_result,
                                                              update_person=chinese_name,
                                                              update_time=timezone.now())
        # 日志记录
        content = f"用户 {chinese_name} 编辑AI生成用例成功，编辑用例id: {case_id}"
        CommonUtil().recordSystemLog(username, content)
        return ResponseUtils.return_success("用例更新成功")

    # 查询核心用例与用例池匹配结果详情
    @action(methods=['GET'], detail=False)
    def get_related_cases(self, request):
        """
           获取关联的核心用例
           1. 根据case_id查询映射关系
           2. 获取关联的normtestcase_ids
           3. 查询这些核心用例的详细信息
           4. 如果任何步骤结果为空，返回空数组
           """
        try:
            case_id = request.GET.get('id', '')
            if not case_id:
                return JsonResponse([], safe=False)
            mapping_ids = CaseMapping.objects.filter(
                case_id=case_id,
                status=0,
                is_active=True
            ).values_list('normtestcase_id', flat=True)
            if not mapping_ids:
                return JsonResponse([], safe=False)

            cases = NormTestCase.objects.filter(
                id__in=mapping_ids,
                is_active=True
            ).annotate(
                original_id=F('id')  # 保留原始ID
            ).values(
                'id',
                'case_scene',
                'premise',
                'test_steps',
                'expected_result'
            )

            return JsonResponse(list(cases), safe=False)

        except Exception as e:
            return JsonResponse([], safe=False)


    # 更新AI生成用例召回状态
    @action(methods=['POST'], detail=False)
    def update_ai_test_case_status(self, request):
        user = UserUtils.get_login_user(request)
        username = user.username
        chinese_name = user.chinese_name
        try:
            temp = json.loads(request.body)
            case_ids = temp.get('id', [])
            status = temp.get('status', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        # 获取当前时间
        update_time = timezone.now()
        # 显式的开启一个事务
        with (transaction.atomic()):
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                # 记录处理的用例数量
                processed_count = 0
                skipped_count = 0

                for case_id in case_ids:
                    # 先查询当前用例的状态
                    current_case = AIGeneratedTestCase.objects.filter(id=case_id).first()
                    # 如果当前状态与目标状态一致，则跳过处理
                    if current_case and current_case.status == status:
                        skipped_count += 1
                        logger.info(f"用例ID {case_id} 当前状态已经是 {status}，跳过处理")
                        continue
                    # 状态不一致，执行更新操作
                    processed_count += 1

                    # 更新用例状态
                    AIGeneratedTestCase.objects.filter(id=case_id).update(
                        status=status,
                        update_person=chinese_name,
                        update_time=update_time
                    )
                    # 将召回用例插入测试用例基础信息表
                    if status == '1':
                        # 已经获取了用例信息，不需要再次查询
                        ai_test_case_info = current_case
                        # 获取AI识别生成用例记录信息
                        ai_recognize_record_info = AIGenerationRecord.objects.filter(
                            id=ai_test_case_info.recognize_record_id).first()
                        # 获取需求信息
                        requirement_info = TapdRequirementInfo.objects.filter(
                            tapd_short_id=ai_test_case_info.requirement_id).first()
                        # 获取迭代信息
                        iteration_info = TapdIterationsInfo.objects.filter(
                            tapd_iteration_id=ai_recognize_record_info.iteration_id).first()
                        # 将用例插入数据库
                        TestCase.objects.create(
                            case_scene=ai_test_case_info.case_scene,
                            business_line=ai_recognize_record_info.business_id,
                            system=ai_test_case_info.system,
                            module=ai_test_case_info.module,
                            requirement_id=ai_test_case_info.requirement_id,
                            requirement=requirement_info.requirement_name,
                            developer=requirement_info.developer,
                            premise=ai_test_case_info.premise,
                            test_steps=ai_test_case_info.test_steps,
                            expected_result=ai_test_case_info.expected_result,
                            iteration_name=iteration_info.iteration_name,
                            case_type=4,
                            operator_result=0,
                            create_time=update_time,
                            update_time=update_time,
                            update_person=case_id,  # 当为召回用例，设置更新人为AI生成测试用例表的id(ai_generated_test_cases.id)
                            keyword='召回用例',
                            is_active=True
                        )
                    # 更新测试用例基础信息表中召回用例状态为失效并打标
                    else:
                        TestCase.objects.filter(update_person=case_id).update(
                            is_active=False,
                            keyword='取消召回用例',
                            update_time=update_time
                        )
            except Exception as e:
                logger.error(f'用例召回状态更新失败：{e}')
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_GENERATION_TESTCASE_FAIL)
            transaction.savepoint_commit(save_id)
        # 日志记录
        content = ''
        if len(case_ids) > 1:
            # 批量更新召回状态
            content = f"用户 {chinese_name} 更新AI生成用例召回状态，处理 {processed_count} 条，跳过 {skipped_count} 条"
        elif len(case_ids) == 1:
            # 单个更新召回状态日志记录
            content = f"用户 {chinese_name} 更新AI生成用例召回状态，用例id: {case_ids[0]}"
        CommonUtil().recordSystemLog(username, content)
        return ResponseUtils.return_success("用例召回状态更新成功")

    # 获取迭代信息列表
    @action(methods=['POST'], detail=False)
    def get_tapd_iteration_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            iteration_name = temp.get('iteration_name', '')
            business_id = temp.get('business_id', '')
            create_person = temp.get('create_person', '')
            pagenum = temp.get('pagenum', 1)
            pagesize = temp.get('pagesize', 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询AI生成用例记录，查询条件：迭代名称=%s，业务线=%s，创建人=%s " \
                  % (username, iteration_name, business_id, create_person)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = TapdIterationsInfo.objects.filter(is_active=True)
        if iteration_name:
            iteration_ids = TapdIterationsInfo.objects.filter(iteration_name__icontains=iteration_name).values_list(
                'tapd_iteration_id', flat=True)
            obj = obj.filter(iteration_id__in=iteration_ids)
        if business_id:
            obj = obj.filter(business_id=business_id)
        obj = obj.order_by('-id')  # 排序
        paginator = Paginator(obj, pagesize)  # 条数
        data = paginator.page(pagenum)  # 页码
        data_list = list(data.object_list.values())  # 查到第一页的数据
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_line"])
            item["businessName"] = businessInfo.business_name
            # total_smoke_case = TestCase.objects.filter(case_report_id=item["id"], case_type=1).count()
            # pass_smoke_case = TestCase.objects.filter(case_report_id=item["id"], case_type=1,
            #                                           operator_result="1").count()
            # item["total_count"] = total_smoke_case
            # item["pass_count"] = pass_smoke_case
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 查找相似用例
    @action(methods=['POST'], detail=False)
    def ai_match_case(self, request):
        # 从请求头中取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.username
        chinese_name = userinfo.chinese_name
        temp = json.loads(request.body)
        try:
            case_report_id = temp.get('report_id', '')
            case_id = temp.get('core_case_id', '')
            if not case_report_id and not case_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "导入记录ID和用例ID不能同时为空")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询相似用例，查询条件：导入记录id=%s，用例id=%s " \
                  % (chinese_name, case_report_id, case_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        try:
            # 以导入记录为维度匹配
            if case_report_id:
                # 获取所有核心用例的用例信息
                test_cases = TestCase.objects.filter(case_report_id=case_report_id, is_active=True, case_type='2')
            else:
                # 以用例为维度匹配,获取用例的详细信息
                test_cases = TestCase.objects.filter(id=case_id, is_active=True, case_type='2')

            if not test_cases.exists():
                logger.warning(f"未找到匹配条件的用例: report_id={case_report_id}, case_id={case_id}")
                return ResponseUtils.return_fail(AppErrors.ERROR_MATCH_TESTCASE_FAIL,
                                                 f"report_id={case_report_id}, case_id={case_id}未找到匹配条件的用例")

            for test_case in test_cases:
                case_id = test_case.id
                CaseMapping.objects.filter(case_id=case_id, is_active=True).update(is_active=False)
                # 拼接匹配需要的文本信息:用例场景/用例步骤/预期结果
                content = ""
                content += "用例场景：%s\n" % test_case.case_scene
                content += "用例步骤：%s\n" % test_case.test_steps
                content += "预期结果：%s\n" % test_case.expected_result
                try:
                    # 调用查询相似用例方法,获取相似用例的ID列表(用例池ID列表)
                    similar_case_ids = query_test_cases(content)
                    if similar_case_ids:
                        # 准备批量创建的对象列表
                        case_mappings = [
                            CaseMapping(
                                normtestcase_id=similar_case_id,
                                case_id=test_case.id,
                                created_time=timezone.now(),
                                status=0,
                                is_active=True
                            )
                            for similar_case_id in similar_case_ids
                        ]
                        # 批量插入
                        CaseMapping.objects.bulk_create(case_mappings)
                        logger.info(f"成功批量插入 {len(case_mappings)} 条匹配用例记录,匹配ID为: {similar_case_ids}")
                    else:
                        logger.info(f"用例ID {test_case.id} 未找到相似用例")
                except Exception as e:
                    logger.error(f"查询相似用例失败: {str(e)}")
                    # 继续处理下一个用例，不中断整个流程
            return ResponseUtils.return_success("匹配成功")
        except Exception as e:
            error_info = traceback.format_exc()
            logger.error(f'相似用例匹配失败：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, f"相似用例匹配失败: {str(e)}")

    # 处理匹配结果中无需处理的记录
    @action(methods=['POST'], detail=False)
    def obsolete_case(self, request):
        try:
            data = json.loads(request.body)
            norm_testcase_id = data.get('norm_testcase_id')
            case_id = data.get('case_id')

            if not norm_testcase_id or not case_id:
                return JsonResponse({
                    'code': 400,
                    'message': '参数不完整，需要norm_testcase_id和case_id'
                }, status=400)

            # 更新匹配记录
            updated = CaseMapping.objects.filter(
                normtestcase_id=norm_testcase_id,
                case_id=case_id
            ).update(status=2)

            if updated == 0:
                return JsonResponse({
                    'code': 404,
                    'message': '未找到匹配的记录'
                }, status=404)

            return JsonResponse({
                'code': 200,
                'message': '成功标记为不处理',
                'data': {
                    'affected_rows': updated
                }
            })

        except Exception as e:
            return JsonResponse({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            }, status=500)


    @action(methods=['POST'], detail=False)
    def push_merge_case(self, request):
        """
        推送合并用例到标准用例池
        """
        user = UserUtils.get_login_user(request)
        username = user.username
        try:
            temp = json.loads(request.body)
            iteration_name = temp.get('iteration_name', '')
            case_report_id = temp.get('case_report_id')

            # 参数校验
            if not all([iteration_name, case_report_id]):
                raise ValueError("缺少必填参数case_report_id")

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        try:
            with transaction.atomic():
                # 初始化
                merge_case_num = 0
                core_case_num = 0
                # 查询中间表匹配数据
                intermediates = MergeCaseIntermediate.objects.filter(
                    iteration_name=iteration_name,
                    case_report_id=case_report_id,
                    status=0,
                    is_active=1
                )
                # 查询核心用例数据
                core_test_cases = TestCase.objects.filter(case_report_id=case_report_id, case_type='2',
                                                          operator_result='0')

                if not intermediates.exists() and not core_test_cases.exists():
                    return ResponseUtils.return_fail(AppErrors.ERROR_PUSH_TESTCASE_FAIL, "无未推送的用例")

                # 批量插入标准用例池
                for inter in intermediates:
                    norm_case = NormTestCase.objects.filter(id=inter.normtestcase_id)
                    norm_case.update(
                        iteration_name=inter.iteration_name,
                        case_scene=inter.case_scene,
                        premise=inter.premise,
                        test_steps=inter.test_steps,
                        expected_result=inter.expected,
                        modifier=user.chinese_name
                    )
                    # 向量化用例池
                    vectorize_test_cases(norm_case)
                    merge_case_num += 1

                # 更新中间表状态
                intermediates.update(
                    status=1
                )
                logger.info(f'成功插入 {merge_case_num} 条合并用例')
                # 将核心用例插入用例池并插入向量库
                if core_test_cases.exists():
                    for core_test_case in core_test_cases:
                        core_norm_case = NormTestCase.objects.create(
                            business_line=core_test_case.business_line,
                            module=core_test_case.module,
                            iteration_name=core_test_case.iteration_name,
                            case_scene=core_test_case.case_scene,
                            premise=core_test_case.premise,
                            test_steps=core_test_case.test_steps,
                            expected_result=core_test_case.expected_result,
                            creator=user.chinese_name,
                            modifier=user.chinese_name
                        )
                        # 将核心用例插入向量库
                        vectorize_test_cases([core_norm_case])
                        core_case_num += 1
                    core_test_cases.update(
                        operator_result='1'
                    )
                    logger.info(f'成功插入 {core_case_num} 条核心用例')
                # 记录操作日志
                content = f"用户 {username} 推送导入记录ID为{case_report_id}的合并用例到标准池"
                CommonUtil().recordSystemLog(username, content)

                return ResponseUtils.return_success({"count": merge_case_num + core_case_num})

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'合并用例推送失败：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, f"内部服务异常：{str(e)}")


    @action(methods=['POST'], detail=False)
    def merge_case(self, request):
        user = UserUtils.get_login_user(request)
        try:
            data = json.loads(request.body)
            norm_case_id = data.get('norm_case_id')
            test_case_id = data.get('test_case_id')

            if not norm_case_id or not test_case_id:
                return ResponseUtils.return_fail(
                    AppErrors.ERROR_ARGS_EXCEPTION,
                    "必须同时提供 norm_case_id 和 test_case_id"
                )

            norm_case = NormTestCase.objects.get(id=norm_case_id)
            test_case = TestCase.objects.get(id=test_case_id)

            context = {
                "norm_case": {k: getattr(norm_case, k) for k in
                              ['case_scene', 'premise', 'test_steps', 'expected_result']},
                "test_case": {k: getattr(test_case, k) for k in
                              ['case_scene', 'premise', 'test_steps', 'expected_result']}
            }

            # 调用AI合并工具·
            ai_response = ai_agent.tools.testcase_tools.ai_merge_case(
                json.dumps(context, ensure_ascii=False)
            )
            logger.info(f'AI合并结果：{ai_response}')
            merge_case = json.loads(ai_response)
            data = {
                    "merged_content": merge_case,
                    "source_info": {
                        "norm_case_id": norm_case_id,
                        "test_case_id": test_case_id,
                        "merge_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "operator": user.chinese_name
                    }
                }
            return ResponseUtils.return_success("用例合并成功", data)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'合并用例失败：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_MERGE_TESTCASE_FAIL, e)

    @action(methods=['POST'], detail=False)
    def merge_case_confirm(self, request):
        """
        确认合并用例接口
        1. 将合并后的用例存入MergeCaseIntermediate表
        2. 更新CaseMapping表状态为已处理(2)
        """
        user = UserUtils.get_login_user(request)
        username = user.username
        chinese_name = user.chinese_name
        try:
            temp = json.loads(request.body)
            iteration_name = temp.get('iteration_name', '')
            case_id = temp.get('case_id', '')
            norm_testcase_id = temp.get('norm_testcase_id', '')
            case_scene = temp.get('case_scene', '')
            premise = temp.get('premise', '')
            test_steps = temp.get('test_steps', '')
            expected_result = temp.get('expected_result', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        nowtime = timezone.now()
        # 查询映射记录
        mapping_records = CaseMapping.objects.filter(
            normtestcase_id=norm_testcase_id,
            case_id=case_id,
            status='0',
            is_active=True
        )
        case_report_id = TestCase.objects.filter(id=case_id).values('case_report_id')
        business_id = TestCase.objects.filter(id=case_id).values('business_line')
        module = TestCase.objects.filter(id=case_id).values('module')
        if not mapping_records.exists():
            return Response({
                "code": "1004",
                "message": "未找到对应的用例映射记录"
            }, status=status.HTTP_404_NOT_FOUND)

        # 显式的开启一个事务
        with (transaction.atomic()):
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                merge_record = MergeCaseIntermediate.objects.create(
                    case_report_id=case_report_id,
                    business_line=business_id,
                    iteration_name=iteration_name,
                    module=module,
                    case_scene=case_scene,
                    premise=premise,
                    test_steps=test_steps,
                    expected=expected_result,
                    creator=chinese_name,
                    normtestcase_id=norm_testcase_id,
                    is_active=True,
                    create_time=nowtime,
                    status=0
                )

                # 更新映射记录状态
                updated_count = mapping_records.update(
                    status='2',  # 已处理状态
                    operation_log=(
                        f"合并确认操作，操作人：{chinese_name}，"
                        f"时间：{nowtime}"
                    )
                )
            except Exception as e:
                logger.error(f'用例合并更新失败：{e}')
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_GENERATION_TESTCASE_FAIL)
            transaction.savepoint_commit(save_id)
        content = f"用户 {chinese_name} 确认更新合并用例，用例id: {norm_testcase_id}"
        CommonUtil().recordSystemLog(username, content)
        return_data = {
            "merge_record_id": merge_record.id,
            "updated_mapping_count": updated_count
        }
        return ResponseUtils.return_success("合并确认成功", return_data)

    @action(methods=['GET'], detail=False)
    def vectorize_test_cases(self, request):
        return ResponseUtils.return_success("开始向量化测试用例",
                                           vectorize_test_cases(NormTestCase.objects.filter(is_active=True)))

    @action(methods=['POST'], detail=False)
    def get_testcases_list(self, request):
        user = UserUtils.get_login_user(request)
        username = user.username
        try:
            temp = json.loads(request.body)
            case_type = temp.get('case_type', 'all_testcase')  # 用例类型
            case_scene = temp.get('case_scene', '')  # 用例场景
            scene_id = temp.get('scene_id', '')  # 场景ID-核心用例tab
            push_status = temp.get('push_status', '')  # 推送状态-核心用例tab,0-未推送，1-已推送
            iteration_name = temp.get('iteration_name', '')  # 迭代名称
            requirement_id = temp.get('requirement_id', '')  # 需求ID(短码)
            business_line = temp.get('business_id', '')  # 业务线
            update_person = temp.get('update_person', '')  # 更新人
            developer = temp.get('developer', '')  # 开发人员-冒烟用例tab
            operator = temp.get('operator', '')  # 执行人员-冒烟用例tab
            operator_result = temp.get('operator_result', [])  # 执行结果-冒烟用例tab
            callback_status = temp.get('callback_status', '')  # 召回状态-AI生成用例tab,0-未召回，1-已召回
            pagenum = temp.get("pagenum", 1)
            pagesize = temp.get("pagesize", 10)

            # 参数校验
            if not all([case_type]):
                raise ValueError("缺少必填参数'用例类型'")

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询用例列表" \
                  % (username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        # 过滤出基础数据
        # 全部用例
        if case_type == 'all_testcase':
            obj = TestCase.objects.filter(is_active=True)
        # 冒烟用例
        if case_type == 'smoke_testcase':
            obj = TestCase.objects.filter(is_active=True, case_type='1')
        # 核心用例
        if case_type == 'core_testcase':
            obj = TestCase.objects.filter(is_active=True, case_type='2')
            # 场景ID-核心用例tab
            if scene_id:
                obj = obj.filter(case_scene_id=scene_id)
            # 推送状态-核心用例tab
            if push_status:
                obj = obj.filter(operator_result=push_status)  # 0-未推送，1-已推送
        # AI生成用例
        if case_type == 'ai_testcase':
            obj = AIGeneratedTestCase.objects.filter(is_active=True)
            # 召回状态-AI生成用例tab,0-未召回，1-已召回
            if callback_status:
                obj = obj.filter(status=callback_status)
        '''
        筛选条件
        '''
        # 迭代名称
        if iteration_name:
            # 通过迭代名称模糊查询出迭代ID
            iteration_ids = TapdIterationsInfo.objects.filter(iteration_name__icontains=iteration_name).values_list(
                'tapd_iteration_id', flat=True)
            # 通过迭代ID过滤出需求ID
            requirement_ids = TapdRequirementInfo.objects.filter(
                iteration_id__in=iteration_ids
            ).values_list("tapd_short_id", flat=True)
            obj = obj.filter(requirement_id__in=requirement_ids)
        # 需求ID
        if requirement_id:
            obj = obj.filter(requirement_id=requirement_id)
        # 业务线
        if business_line:
            obj = obj.filter(business_line=business_line)
        # 用例场景
        if case_scene:
            obj = obj.filter(case_scene__icontains=case_scene)
        # 更新人
        if update_person:
            obj = obj.filter(update_person__icontains=update_person)
        # 开发人员
        if developer:
            obj = obj.filter(developer__icontains=developer)
        # 执行人
        if operator:
            obj = obj.filter(operator__icontains=operator)
        # 执行结果
        if operator_result:
            # 处理 operator_result 为列表格式
            if isinstance(operator_result, str):
                # 兼容前端字符串
                operator_result = operator_result.split(',') if operator_result else []
            elif not isinstance(operator_result, list):
                # 非列表类型强制转为空列表
                operator_result = []
            obj = obj.filter(operator_result__in=operator_result)

        # 按照字段进行排序
        obj = obj.order_by('-id')
        # 分页
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        # 将数据转换为列表
        data_list = list(data.object_list.values())
        for item in data_list:
            if item['business_line']:
                businessInfo = BusinessInfo.objects.get(business_id=item["business_line"])  # 转化业务名称
                item["businessName"] = businessInfo.business_name
            if item.get("file_url"):
                item["file_url"] = json.loads(item["file_url"])
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)



